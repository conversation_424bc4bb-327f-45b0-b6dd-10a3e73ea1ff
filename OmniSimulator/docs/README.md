# 📖 Embodied Simulator Documentation Center

Welcome to the Embodied Simulator documentation center! Here you'll find complete documentation resources to help you get started quickly and understand the system in depth.

## 📚 Documentation Navigation

### 🚀 Quick Start
- **[Project README](../README.md)** - Project overview and quick start guide
- **[User Manual](user_manual.md)** - Detailed usage guide and configuration instructions

### 🔧 Development Documentation
- **[API Documentation](api.md)** - Complete API reference and usage examples
- **[Architecture Documentation](architecture.md)** - System architecture and design principles
- **[Developer Guide](developer_guide.md)** - Development environment setup and contribution guide

### 📋 Feature Documentation
- **[Action System](actions.md)** - Action types, configuration, and extension guide
- **[Real-time Action Descriptions](real_time_action_descriptions.md)** - Intelligent action description system detailed explanation
- **[Environment Description Configuration](environment_description.md)** - Environment description configuration details and best practices
- **[Visualization System](visualization.md)** - Web interface usage and configuration guide
- **[Dynamic Registration Mechanism](dynamic_action_registration.md)** - Action registration system detailed explanation

## 🎯 Classification by Role

### 👤 End Users
If you are using Embodied Simulator for the first time:

1. **[项目README](../README.md)** - 了解项目概况
2. **[用户手册](user_manual.md)** - 学习基本使用方法
3. **[可视化文档](visualization.md)** - 了解Web界面功能

### 🔬 研究人员
如果您要使用系统进行研究：

1. **[API文档](api.md)** - 学习编程接口
2. **[动作系统](actions.md)** - 了解可用的动作类型
3. **[实时动作描述](real_time_action_descriptions.md)** - 了解智能动作描述功能
4. **[架构文档](architecture.md)** - 理解系统设计

### 💻 开发者
如果您要参与开发或扩展系统：

1. **[开发者指南](developer_guide.md)** - 设置开发环境
2. **[架构文档](architecture.md)** - 理解系统架构
3. **[动态注册文档](dynamic_action_registration.md)** - 学习扩展机制

## 📖 文档概览

### [API文档](api.md)
- **内容**: 完整的API参考、使用示例、错误处理
- **适用对象**: 开发者、研究人员
- **更新频率**: 随代码更新

### [用户手册](user_manual.md)
- **内容**: 安装指南、配置说明、使用场景、故障排除
- **适用对象**: 所有用户
- **更新频率**: 定期更新

### [架构文档](architecture.md)
- **内容**: 系统架构、设计原则、核心组件、扩展机制
- **适用对象**: 开发者、系统架构师
- **更新频率**: 架构变更时更新

### [开发者指南](developer_guide.md)
- **内容**: 开发环境、代码规范、测试指南、贡献流程
- **适用对象**: 贡献者、开发者
- **更新频率**: 开发流程变更时更新

### [动作系统文档](actions.md)
- **内容**: 动作类型、配置方法、扩展指南、最佳实践
- **适用对象**: 开发者、高级用户
- **更新频率**: 动作系统更新时

### [实时动作描述文档](real_time_action_descriptions.md)
- **内容**: 智能动作描述系统、实时更新机制、API使用、应用场景
- **适用对象**: 开发者、研究人员、高级用户
- **更新频率**: 动作描述功能更新时

### [可视化文档](visualization.md)
- **内容**: Web界面使用、配置选项、API接口、故障排除
- **适用对象**: 所有用户
- **更新频率**: 可视化功能更新时

### [环境描述配置](environment_description.md)
- **内容**: 环境描述配置详解、最佳实践、性能考虑
- **适用对象**: 开发者、研究人员
- **更新频率**: 配置系统变更时

### [动态注册文档](dynamic_action_registration.md)
- **内容**: 注册机制、数据驱动设计、测试框架
- **适用对象**: 开发者、系统集成者
- **更新频率**: 注册机制变更时

## 🔍 快速查找

### 常见任务
- **安装系统** → [用户手册 - 安装和配置](user_manual.md#安装和配置)
- **运行第一个示例** → [项目README - 快速开始](../README.md#快速开始)
- **配置环境描述** → [环境描述配置 - 配置参数详解](environment_description.md#配置参数详解)
- **获取动作描述** → [实时动作描述 - API使用](real_time_action_descriptions.md#api使用)
- **配置可视化** → [可视化文档 - 配置说明](visualization.md#配置说明)
- **添加新动作** → [动作系统 - 扩展指南](actions.md#扩展指南)
- **API使用** → [API文档 - 核心API](api.md#核心api)

### 问题解决
- **安装问题** → [用户手册 - 故障排除](user_manual.md#故障排除)
- **可视化问题** → [可视化文档 - 故障排除](visualization.md#故障排除)
- **开发问题** → [开发者指南 - 常见问题](developer_guide.md#常见问题)
- **性能问题** → [架构文档 - 性能考虑](architecture.md#性能考虑)

### 扩展开发
- **自定义动作** → [动作系统 - 扩展指南](actions.md#扩展指南)
- **新场景** → [开发者指南 - 测试指南](developer_guide.md#测试指南)
- **可视化扩展** → [可视化文档 - 扩展开发](visualization.md#扩展开发)
- **系统集成** → [API文档 - 完整使用示例](api.md#完整使用示例)

## 📝 文档维护

### 更新原则
- **准确性**: 确保文档与代码同步
- **完整性**: 覆盖所有主要功能
- **易读性**: 使用清晰的语言和示例
- **实用性**: 提供可操作的指导

### 贡献文档
如果您发现文档问题或希望改进文档：

1. **报告问题**: 通过GitHub Issues报告文档问题
2. **提交改进**: 提交Pull Request改进文档
3. **建议内容**: 建议需要补充的文档内容

### 文档规范
- **格式**: 使用Markdown格式
- **结构**: 清晰的标题层次和目录
- **示例**: 提供可运行的代码示例
- **链接**: 适当的内部和外部链接

## 🆘 获取帮助

### 文档相关
- **文档问题**: 在GitHub Issues中标记为`documentation`
- **内容建议**: 提交Feature Request
- **翻译需求**: 联系维护团队

### 技术支持
- **使用问题**: 查看[用户手册](user_manual.md)和[API文档](api.md)
- **开发问题**: 参考[开发者指南](developer_guide.md)
- **Bug报告**: 通过GitHub Issues报告

### 社区资源
- **GitHub仓库**: 源代码和问题跟踪
- **讨论区**: 技术讨论和经验分享
- **示例项目**: 实际使用案例

## 📊 文档统计

| 文档 | 页数 | 最后更新 | 状态 |
|------|------|----------|------|
| API文档 | ~15页 | 最新 | ✅ 完整 |
| 用户手册 | ~12页 | 最新 | ✅ 完整 |
| 架构文档 | ~10页 | 最新 | ✅ 完整 |
| 开发者指南 | ~8页 | 最新 | ✅ 完整 |
| 动作系统 | ~15页 | 最新 | ✅ 完整 |
| 可视化文档 | ~8页 | 最新 | ✅ 完整 |
| 动态注册 | ~6页 | 最新 | ✅ 完整 |

---

📧 **联系我们**: 如有任何文档相关问题，请通过GitHub Issues联系我们。

🔄 **最后更新**: 2024年6月 | **版本**: v1.0.0
