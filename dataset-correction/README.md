# 数据集矫正工具

一个用于人工评估和矫正AI模型执行失败任务的Web工具。

## 功能特性

- 🔍 **自动识别失败任务**: 从CSV日志中筛选status为"failed"的任务
- 📝 **JSON在线编辑**: 使用JSONEditor提供专业的JSON编辑体验
  - 默认使用代码模式，支持语法高亮和直接编辑
  - 默认开启搜索框，快速定位内容
  - 支持代码、树形、表单三种编辑模式切换
  - 搜索、排序、历史记录功能
  - 特别适合编辑空的`states: {}`对象
- 🎯 **人工评估流程**: 两步评估流程，记录任务完成情况
- 💾 **自动保存**: JSON修改和评估结果自动保存
- 🔄 **进度管理**: 支持任务导航、跳转和进度跟踪
- ⌨️ **快捷键支持**: Ctrl+左右箭头切换任务，Ctrl+S保存评估
- 🔄 **重置功能**: 支持将JSON编辑器重置到原始状态
- 👁️ **只读轨迹**: 轨迹数据以只读模式显示，防止意外修改
- 🎯 **智能展开**: 任务定义展开tasks所有子节点，场景配置展开objects数组

## 安装和使用

### 1. 安装依赖
```bash
cd dataset-correction
npm install
```

### 2. 启动服务
```bash
npm start
```

### 3. 访问工具
打开浏览器访问: http://localhost:3000

## 界面说明

### 布局结构
- **顶部**: 任务定义JSON编辑器（横向布局，智能展开tasks所有子节点，收起其他节点）
- **左下**: 场景配置JSON编辑器（纵向布局，智能展开objects数组，收起其他节点）
- **右下**: 轨迹数据JSON编辑器（纵向布局，只读模式，默认展开所有级别）
- **右侧**: 人工评估面板

### 评估流程
1. **任务是否可以完成？**
   - 选择"是": 继续第二个问题
   - 选择"否": 记录结果为None

2. **4o完成得对吗？**（仅当第一个问题选择"是"时显示）
   - 选择"对": 记录结果为True
   - 选择"不对": 记录结果为False

### 操作说明
- **JSON编辑**: 默认使用代码模式，直接编辑JSON文本，自动保存到文件（轨迹数据为只读）
- **搜索功能**: 默认显示搜索框，支持快速定位和高亮显示
- **添加属性/值**:
  - **代码模式**（推荐）: 直接在JSON中添加，如在`"states": {}`中添加属性
  - **树形模式**: 右键点击对象 → "Insert" → 选择类型
  - **表单模式**: 表单式编辑界面
- **编辑模式切换**: 支持代码、树形、表单三种模式，点击菜单栏切换
- **重置功能**: 点击编辑器标题栏的"重置"按钮恢复到原始状态
- **评估保存**: 点击"保存评估"按钮保存评估结果
- **任务导航**: 使用"上一个"/"下一个"按钮或跳转功能
- **快捷键**:
  - `Ctrl + ←/→`: 切换任务
  - `Ctrl + S`: 保存评估
  - `Ctrl + F`: 在代码模式下查找内容

## 数据文件

### 输入文件
- `raw_output/*/subtask_execution_log.csv`: 任务执行日志
- `raw_output/*/trajectories/*.json`: 执行轨迹文件
- `data/eval/*/scene/*.json`: 场景配置文件
- `data/eval/*/task/*.json`: 任务定义文件

### 输出文件
- `evaluation_results.csv`: 人工评估结果
  - dataset_type: 数据集类型(single/multi)
  - scenario_id: 场景编号
  - task_index: 任务索引
  - task_category: 任务类别
  - task_description: 任务描述
  - evaluation_result: 评估结果(True/False/None)
  - timestamp: 评估时间
  - notes: 备注信息

## 技术架构

### 前端
- 纯HTML + JavaScript + CSS
- JSONEditor: 专业JSON编辑器
- 响应式设计，支持不同屏幕尺寸

### 后端
- Node.js + Express
- 文件系统操作
- CSV读写处理

### 特性
- 自动备份原文件
- 实时保存修改
- 进度持久化
- 错误处理和状态提示
- 智能节点展开：
  - 任务定义：展开tasks数组及其所有子节点，收起其他根级节点
  - 场景配置：展开objects数组及每个对象的第一层，收起其他根级节点

## 注意事项

1. **文件路径**: 确保相对路径正确指向数据文件
2. **权限**: 确保有读写数据文件的权限
3. **备份**: 工具会自动备份原文件，但建议手动备份重要数据
4. **浏览器**: 推荐使用现代浏览器以获得最佳体验

## 故障排除

### 常见问题
1. **端口占用**: 修改server.js中的PORT变量
2. **文件路径错误**: 检查DATA_PATHS配置
3. **权限问题**: 确保对数据目录有读写权限
4. **JSON格式错误**: 使用编辑器的验证功能检查格式

### 日志查看
服务器日志会显示在控制台中，包含错误信息和操作记录。
