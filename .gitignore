# ============================================================================
# OmniEmbodied Data Generation Pipeline - Git Ignore Rules
# ============================================================================

# ============================================================================
# Output and Generated Data
# ============================================================================
# Ignore output directory (statistics and summaries)
output/

# Ignore generated JSON files in data subdirectories but keep the folders
# data/clue/*.json
# data/scene/*.json
# data/task/*.json

# Ignore generated JSON files in simulator data subdirectories
simulator/data/scene/*.json
simulator/data/task/*.json

A-save/
dataset-correction/node_modules/
scene_backup_20250724_013542/
raw_output/

# ============================================================================
# Logs and Project Specific
# ============================================================================
# Log files
logs/
data_generation/logs/

# Zone.Identifier files (Windows download metadata)
*:Zone.Identifier

# Python cache files
__pycache__/
*.py[cod]
*$py.class

# Distribution / packaging
.Python
build/
develop-eggs/
dist/
downloads/
eggs/
.eggs/
lib/
lib64/
parts/
sdist/
var/
wheels/
*.egg-info/
.installed.cfg
*.egg
MANIFEST

# PyInstaller
*.manifest
*.spec

# Installer logs
pip-log.txt
pip-delete-this-directory.txt

# Unit test / coverage reports
htmlcov/
.tox/
.coverage
.coverage.*
.cache
nosetests.xml
coverage.xml
*.cover
.hypothesis/
.pytest_cache/

# Translations
*.mo
*.pot

# Django stuff:
*.log
local_settings.py
db.sqlite3

# Flask stuff:
instance/
.webassets-cache

# Scrapy stuff:
.scrapy

# Sphinx documentation
docs/_build/

# PyBuilder
target/

# Jupyter Notebook
.ipynb_checkpoints

# pyenv
.python-version

# celery beat schedule file
celerybeat-schedule

# SageMath parsed files
*.sage.py

# Environments
.env
.venv
env/
venv/
ENV/
env.bak/
venv.bak/

# Spyder project settings
.spyderproject
.spyproject

# Rope project settings
.ropeproject

# mkdocs documentation
/site

# mypy
.mypy_cache/
.dmypy.json
dmypy.json

# IDE files
.vscode/
.idea/
*.swp
*.swo
*~

# ============================================================================
# macOS System Files
# ============================================================================
# General
.DS_Store
.DS_Store?
.AppleDouble
.LSOverride

# Icon must end with two \r
Icon

# Thumbnails
._*

# Files that might appear in the root of a volume
.DocumentRevisions-V100
.fseventsd
.Spotlight-V100
.TemporaryItems
.Trashes
.VolumeIcon.icns
.com.apple.timemachine.donotpresent

# Directories potentially created on remote AFP share
.AppleDB
.AppleDesktop
Network Trash Folder
Temporary Items
.apdisk

# Windows
ehthumbs.db
Thumbs.db

# Temporary files
*.tmp
*.temp

# ============================================================================
# API Keys and Secrets (Security)
# ============================================================================
# Environment files with API keys
.env.local
.env.*.local
secrets.json
api_keys.json

# ============================================================================
# Project Specific
# ============================================================================
# Backup files
*.bak
*.backup

# Test outputs
test_output/
test_results/

# Development databases
*.db
*.sqlite
*.sqlite3
