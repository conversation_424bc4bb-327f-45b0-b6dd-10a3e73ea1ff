python examples/single_agent_example.py \
    --model llama8b \
    --observation-mode explore \
    --suffix llama8b_single_wo \
    --parallel \
    --config-override "parallel_evaluation.scenario_parallelism.max_parallel_scenarios=20"

python examples/centralized_agent_example.py \
    --model llama8b \
    --observation-mode explore \
    --suffix llama8b_multi_wo \
    --parallel \
    --config-override "parallel_evaluation.scenario_parallelism.max_parallel_scenarios=20"