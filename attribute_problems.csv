scene_id,dataset_type,task_index,check_index,object_id,attribute_name,task_expected_value,csv_expected_value,matched_action,task_description
00007,single-independent,0,0,malfunctioning_vending_machine_1,is_screwed_in,False,False,unscrew,Use the screwdriver_1 to unscrew the malfunctioning_vending_machine_1.
00008,single-independent,0,1,overhead_projector_1,is_plugged_in,True,,,"Find the pink USB drive, plug it into the secure_terminal_1, and turn off the overhead_projector_1."
00011,single-independent,0,0,patchbay_1,is_screwed_in,True,True,screw,Use the gold_screwdriver_1 to fix the loose patchbay_1.
00012,single-independent,0,0,tube_preamp_1,is_screwed_in,True,True,screw,"Find the silver, Universal Audio branded preamp that is broken and use the gold_screwdriver_1 to repair it."
00020,single-independent,0,0,blueprint_tube_1,is_open,True,True,open,Use the seal_opener_1 to unseal the blueprint_tube_1 labeled 'WEST RIVER BRIDGE—FINAL' and place it on the light_table_1.
00032,single-independent,0,0,conference_table_1,is_broken,False,False,repair,"Use the repair kit to fix the largest, metal table's only flashing red device."
00044,single-independent,0,0,microwave_1,is_adjusted,True,True,adjust,Use the screwdriver_1 to adjust the play microwave with the jammed door.
00046,single-independent,0,0,broken_monitor_1,is_open,False,True,open,Repair the broken monitor in the supply closet.
00050,single-independent,0,0,tricycle_1,is_mashed,True,True,mash,Repair the broken tricycle_1 using the masher_1.
00065,single-independent,0,0,led_screen_3,is_ripped,True,True,rip,Find the LED screen displaying 'diplomatic cable transcripts' and turn it on.
00080,single-independent,0,0,printer_1,is_broken,False,False,repair,Use the toolbox to repair the jammed printer on the map table.
00083,single-independent,0,0,broken_coffee_machine_1,is_broken,False,False,repair,Repair the broken_coffee_machine_1.
00092,single-independent,0,0,adjustable_desk_1,is_raked,True,True,rake,Find the largest wooden furniture in the central coding hub and use the rake to smooth its surface.
00098,single-independent,0,0,laser_housing_unit_1,is_soldered,True,True,solder,Use the soldering_iron_1 to solder the laser_housing_unit_1.
00129,single-independent,0,0,encrypted_hard_drive_1,is_formatted,True,True,format,Use the computer to format the encrypted hard drive labeled 'EFP_LOGS'.
00130,single-independent,0,0,thermal_printer_1,is_printed,True,True,print,Fix the jammed thermal printer.
00133,single-independent,0,0,flour_sack_2,is_weighed,True,True,weigh,Use the digital_scale_1 to weigh the flour_sack_2.
00134,single-independent,0,0,beef_box_1,is_wet,False,False,dry,Find the leaking beef box and use the cleaning_towel_1 to dry it before placing it in the walk-in fridge.
00148,single-independent,0,0,contested_pirlo_jersey_1,is_adjusted,True,True,adjust,Use the uv_torch_1 to adjust the contested_pirlo_jersey_1.
00152,single-independent,0,0,eye_wash_station_1,is_dirty,False,False,clean,Clean the dusty eye_wash_station_1 using the ultrasonic_cleaner_1.
00153,single-independent,0,0,chemical_bottle_2,is_mixed,True,True,mix,Use the mixing bowl and spoon to mix the nutrient solution in chemical_bottle_2.
00156,single-independent,0,0,sealed_treaty_folio_1,is_paired,True,True,pair,Repair the sealed_treaty_folio_1 using the wax_seal_kit_1.
00160,single-independent,0,0,broken_bat_1,is_paired,True,True,pair,Repair the broken_bat_1 using the hot_glue_gun_1.
00161,single-independent,0,0,half_stitched_glove_1,is_paired,True,True,pair,Use the leatherman_multi_tool_1 to repair the half_stitched_glove_1 and place it in the metal_locker_1.
00176,single-independent,0,0,microscope_1,is_paired,True,True,pair,Repair the broken microscope (microscope_1) in the wet lab.
00177,single-independent,0,0,broken_centrifuge_1,is_paired,True,True,pair,Use the toolbox to repair the broken centrifuge (broken_centrifuge_1) in the wet lab.
00181,single-independent,0,0,coffee_cup_1,is_washed,False,False,wash,Use the cleaning kit to wash the stained coffee cup on the teleprompter.
00188,single-independent,0,0,air_compressor_1,is_paired,True,True,pair,Repair the air_compressor_1 using the repair_kit_1.
00196,single-independent,0,0,vacuum_cleaner_1,is_dirty,False,False,clean,Recharge the vacuum_cleaner_1 using the barcode_scanner_1.
00216,single-independent,0,0,green_screen_panel_2,is_sprayed,True,True,spray,Use the spray_bottle_1 to clean the dirty green screen panel.
00228,single-independent,0,0,burp_animation_debugger_tablet_1,is_screwed_in,True,True,screw,Repair the burp_animation_debugger_tablet_1 using the precision_screwdriver_set_1.
00234,single-independent,0,0,microscope_station_1,is_polished,True,True,polish,Use the decontamination_cloth_1 and chemical_polish_1 to polish the microscope_station_1.
00241,single-independent,0,0,broken_binoculars_1,is_scraped,True,True,scrape,Repair the broken binoculars using the sample_scraper.
00242,single-independent,0,0,egg_incubator_1,is_measured,True,True,measure,Measure the dimensions of the egg_incubator_1 using the digital calipers and record the data.
00245,single-independent,0,0,iron_door_1,is_loose,True,True,loosen,Use the forge_gauntlet_1 to loosen the reinforced iron door.
00246,single-independent,0,0,scrap_cloth_1,is_covered,True,True,cover,Use the straight_razor_1 to cut the scrap_cloth_1 and cover the broken_lantern_1 with it.
00246,single-independent,0,1,broken_lantern_1,is_covered,True,True,cover,Use the straight_razor_1 to cut the scrap_cloth_1 and cover the broken_lantern_1 with it.
00254,single-independent,0,0,tied_cable_1,is_ripped,True,True,rip,Use the x_acto_knife_1 to rip the tied_cable_1.
00257,single-independent,0,0,wire_cooling_rack_1,is_loose,False,False,tighten,Use the wrench_1 to tighten the wobbly wire_cooling_rack_1.
00258,single-independent,0,0,industrial_oven_1,is_scanned,True,True,scan,Use the laser_thermometer_1 to scan the temperature of the industrial_oven_1 and turn it off if it exceeds 330°C.
00269,single-independent,0,0,leaking_hydraulic_line_1,is_replaced,True,True,replace,Use the hydrospanner_1 to replace the leaking_hydraulic_line_1 on the mini_sub_b_1.
00276,single-independent,0,0,cable_box_1,is_sealed,True,True,seal,Use the toolkit to seal the overfilled cable box.
00277,single-independent,0,0,ddr_mat_1,is_sealed,True,True,seal,"Find the DDR dance mat that is torn at the corner, use the toolkit to seal it, and place it in the tech storage corner."
00284,single-independent,0,0,central_workbench_1,is_sanded,True,True,sand,Use the sandpaper_1 to smooth the scratched central_workbench_1.
00292,single-independent,0,1,lantern_1,is_open,True,True,open,Use the screwdriver_1 to open the metal_toolbox_1 and turn on the lantern_1.
00295,single-independent,0,0,spill_containment_kit_1,is_wet,False,False,dry,Use the spill_containment_kit_1 to dry the wet area in the main_factory_floor.
00296,single-independent,0,0,shattered_obsidian_dagger_1,is_sharp,True,True,sharpen,Use the sharpener_1 to sharpen the shattered_obsidian_dagger_1.
00305,single-independent,0,0,blade_server_1,is_adjusted,True,True,adjust,Find the server with the struggling fan and use the screwdriver_1 to adjust it.
00335,single-independent,0,0,training_dummy_1,is_sterile,True,True,sterilize,Sterilize the training_dummy_1 using the sterilizer_1.
00338,single-independent,0,0,whiteboard_1,is_sprayed,True,True,spray,Use the spray_bottle_1 to clean the whiteboard_1.
00339,single-independent,0,0,olfactory_dispenser_1,is_verified,True,True,verify,Use the fine_gauge_wire_1 to verify the condition of the olfactory_dispenser_1's nozzles.
00342,single-independent,0,0,hologram_projector_1,is_paired,True,True,pair,Repair the broken hologram_projector_1 using the available tools.
00343,single-independent,0,0,hologram_projector_1,is_paired,True,True,pair,Find the only flashing red device (hologram_projector_1) and repair it using the available tools.
00346,single-independent,0,0,coffee_stains_1,is_vacuumed,False,False,vacuum,Clean the coffee stains using the vacuum_cleaner_1.
00354,single-independent,0,0,train_poster_1,is_mounted,True,True,mount,Mount the train_poster_1 on the pegboard_wall_1 using the electrical_tape_1.
00355,single-independent,0,0,chemical_bottle_2,is_pure,False,False,contaminate,Find the only item with the label 'corrosive' and use it to contaminate the chemical_bottle_2.
00366,single-independent,0,0,train_car_712_1,is_assembled,False,False,disassemble,Use the wrench_1 to disassemble the only object in the train_inspection_bay that is both metal and has a condition of 'dented'.
00369,single-independent,0,0,steel_shelving_units_1,is_loose,False,False,tighten,Use the hex_key_set_1 to tighten the loose components of the steel_shelving_units_1.
00380,single-independent,0,0,soldering_iron_1,is_labeled,True,True,label,Use the soldering_iron_1 to sharpen the loose screws on the red_labeled_power_supply_1.
00380,single-independent,0,1,red_labeled_power_supply_1,is_labeled,True,True,label,Use the soldering_iron_1 to sharpen the loose screws on the red_labeled_power_supply_1.
00387,single-independent,0,0,backer_rod_foam_1,is_wrapped,True,True,wrap,Use the duct_tape_1 to wrap the torn backer_rod_foam_1 and place it in the hazardous_waste_bin_1.
00390,single-independent,0,0,robotic_retrieval_arm_1,is_scraped,True,True,scrape,Use the red hexagonal wrench to scrape the frozen robotic retrieval arm.
00393,single-independent,0,0,hydroponic_bed_1,is_screwed_in,True,True,screw,Repair the flickering hydroponic_bed_1 using the screwdriver_1.
00394,single-independent,0,0,hybrid_flowers_1,is_weighed,True,True,weigh,Use the scale_1 to weigh the hybrid_flowers_1 and record the result.
00396,single-independent,0,0,autoclave_1,is_pressure_checked,True,,,Use the autoclave_wrench_1 to check the pressure on the autoclave_1.
00397,single-independent,0,0,autoclave_1,is_pressure_checked,True,,,Use the special_wrench_1 to check the pressure on the autoclave_1 and start its operation.
00397,single-independent,0,1,autoclave_1,is_pressure_checked,True,,,Use the special_wrench_1 to check the pressure on the autoclave_1 and start its operation.
00399,single-independent,0,0,warp_stabilizer_1,is_screwed_in,True,True,screw,Repair the warp_stabilizer_1 using the screwdriver_1.
00400,single-independent,0,0,warp_stabilizer_1,is_screwed_in,True,True,screw,"Use the screwdriver_1 to repair the largest, steel object that is broken and place it in the main_atrium."
00404,single-independent,0,0,decoy_ledger_1,is_solved,True,,,Use the cipher_wheel_1 to solve the puzzle in the decoy_ledger_1.
00406,single-independent,0,0,rotary_evaporator_1,is_on,False,,iron,"Find the heaviest, glass piece of furniture in the main_research_lab and turn it off."
00408,single-independent,0,0,microscope_1,is_dusted,False,False,dust,Use the dust_cloth_1 to dust the microscope_1 and then place it in the ethnobotany_reference_library.
00412,single-independent,0,0,pressure_sensor_array_1,is_pressed,True,True,press,Use the multimeter_1 to scan the pressure_sensor_array_1 and then attach it to the wind_tunnel_duct_1.
00415,single-independent,0,0,locked_cabinet_1,is_screwed_in,False,False,unscrew,Use the toolkit_1 to unscrew the locked_cabinet_1.
00416,single-independent,0,0,workbench_1,is_open,False,False,close,Find the only wooden item in the supply_closet and use the toolkit_1 to repair it.
00419,single-independent,0,0,cargo_truck_1,is_pumped,True,True,pump,Use the jerrycan_1 to pump fuel into the cargo_truck_1.
00423,single-independent,0,0,logic_board_1,is_loose,True,True,loosen,Use the torque screwdriver set to loosen the water-damaged logic board.
00438,single-independent,0,0,patch_bay_1,is_welded,True,True,weld,Use the toolbox to weld the loose cables in the patch_bay_1.
00449,single-independent,0,0,mash_tun_1,is_screwed_in,True,True,screw,Use the screwdriver_1 to fix the stuck_valve condition of the mash_tun_1.
00450,single-independent,0,0,digital_thermometer_1,is_screwed_in,True,True,screw,Use the screwdriver_1 to fix the only flickering device in the brewing_station and then turn it off.
00450,single-independent,0,1,digital_thermometer_1,is_screwed_in,True,True,screw,Use the screwdriver_1 to fix the only flickering device in the brewing_station and then turn it off.
00461,single-independent,0,0,tractor_trailer_1,is_broken,True,True,damage,Use the jaw_of_life_1 to damage the flipped tractor_trailer_1.
00466,single-independent,0,0,disassembled_fuel_tank_1,is_inflated,True,True,inflate,Use the air compressor to inflate the disassembled fuel tank.
00476,single-independent,0,0,locked_footlocker_1,is_open,True,True,open,Use the flathead_screwdriver_1 to open the locked_footlocker_1.
00483,single-independent,0,0,past_ops_folder_1,is_wrapped,False,False,unwrap,Use the scissors_1 to unwrap the past_ops_folder_1.
00487,single-independent,0,0,primary_node_3b_1,is_loose,False,False,tighten,Use the precision_screwdriver_1 to tighten the cooling fan on the primary_node_3b_1.
00490,single-independent,0,0,diagnostic_tool_1,is_engine_on,True,,,Use the specialized_adapter_1 to start the engine of the diagnostic_tool_1 and then place it on the compliance_terminal_1.
00506,single-independent,0,0,ide_usb_adapter_1,is_plugged_in,True,,,Use the screwdriver_1 to tighten the missing screw on the ide_usb_adapter_1 and then plug it into the psu_rig_1.
00506,single-independent,0,1,ide_usb_adapter_1,is_plugged_in,True,,,Use the screwdriver_1 to tighten the missing screw on the ide_usb_adapter_1 and then plug it into the psu_rig_1.
00510,single-independent,0,0,network_switch_1,is_verified,True,True,verify,Use the multimeter_1 to verify the status of the network_switch_1.
00520,single-independent,0,0,warning_light_1,is_broken,False,False,repair,Use the screwdriver_1 to repair the only flashing red warning_light_1 in the quantum_chamber.
00523,single-independent,0,0,broken_cooling_pump_1,is_sealed,True,True,seal,Repair the broken_cooling_pump_1 using the sealant_gun_1.
00524,single-independent,0,0,heavy_reference_book_1,is_sanded,True,True,sand,Find the torn heavy reference book and repair it with the sandpaper_1.
00531,single-independent,0,0,field_notebooks_1,is_sprayed,True,True,spray,Use the decon_sprayer_wand_1 to spray the field_notebooks_1.
00535,single-independent,0,0,musket_1,is_screwed_in,True,True,screw,Repair the broken musket_1 using the screwdriver_1.
00538,single-independent,0,0,coffee_mug_1,is_rinsed,True,True,rinse,Use the water_bottle_1 to rinse the chipped coffee mug.
00539,single-independent,0,0,potted_cactus_1,is_carved,True,True,carve,Use the carving_knife_1 to carve the dead potted cactus and place it in the mini_fridge_1.
00543,single-independent,0,0,industrial_workbench_1,is_broken,False,False,repair,"Use the repair_toolkit_1 to fix the largest, wooden item in the main_workbench_area that is broken."
00571,single-independent,0,0,thermal_gloves_1,is_broken,False,False,repair,Use the sewing_kit_1 to repair the frayed thermal_gloves_1.
00579,single-independent,0,0,footprint_zone_1,is_photographed,True,True,photograph,Use the camera_1 to photograph the footprint_zone_1 and then sync the images with the gps_tracker_1.
00579,single-independent,0,1,gps_tracker_1,is_photographed,True,True,photograph,Use the camera_1 to photograph the footprint_zone_1 and then sync the images with the gps_tracker_1.
00586,single-independent,0,0,typewriter_case_1,is_typed,True,True,type,Use the key_1 to unlock the typewriter_case_1 and then place it on the drafting_table_1.
00590,single-independent,0,0,pulse_oximeter_1,is_calibrated,True,True,calibrate,"Find the white pulse oximeter with low battery, use the calibration_tool_1 to calibrate it, and place it on the operating_table_1."
00597,single-independent,0,0,tactical_tablet_1,is_polished,True,True,polish,Repair the cracked screen of tactical_tablet_1 using the polish_cloth_1.
00604,single-independent,0,0,cracked_vial_1,is_sterile,True,True,sterilize,Sterilize the cracked_vial_1 using the para_film_1.
00611,single-independent,0,0,camera_1,is_dirty,False,False,clean,Use the lens cleaning kit to clean the Canon EOS R5 with RF 15-35mm Lens.
00612,single-independent,0,0,camera_1,is_dirty,False,False,clean,Use the lens cleaning kit to clean the only camera with image stabilization disabled and then photograph it.
00616,single-independent,0,0,central_console_1,is_measured,True,True,measure,Use the precision measurement set to measure and then repair the flickering monitor on the central console.
00623,single-independent,0,0,framed_photo_1,is_cracked,True,True,crack,Use the epoxy to repair the cracked framed team photo and place it on the metal desk.
00635,single-independent,0,0,centrifuge_1,is_screwed_in,True,True,screw,Use the screwdriver_1 to fix the centrifuge_1 that is making a grinding sound.
00646,single-independent,0,0,umbrella_stand_1,is_glued,True,True,glue,Repair the broken umbrella stand using the glue dispenser.
00655,single-independent,0,0,cisco_catalyst_9500_1,is_pasted,True,True,paste,"Find the only broken Cisco device in the server_alley, use the thermal_paste_1 to repair it, and then turn it on."
00655,single-independent,0,1,cisco_catalyst_9500_1,is_pasted,True,True,paste,"Find the only broken Cisco device in the server_alley, use the thermal_paste_1 to repair it, and then turn it on."
00658,single-independent,0,0,keyboard_1,is_wet,False,False,wipe,Use the Cleaning Rag (cleaning_rag_1) to wipe the Sticky Keyboard (keyboard_1).
00661,single-independent,0,0,multimeter_1,is_measured,True,True,measure,Use the multimeter_1 to measure the continuity of the blackberry_10_1.
00662,single-independent,0,0,prying_picks_1,is_open,True,True,open,Use the sharpener_1 to sharpen the prying_picks_1 and then use them to open the tool_caddy_1.
00672,single-independent,0,0,emergency_stop_button_1,is_screwed_in,True,True,screw,Use the screwdriver_1 to tighten the loose emergency_stop_button_1.
00673,single-independent,0,0,interrogation_table_1,is_screwed_in,True,True,screw,"Find the largest, wooden table in the secondary inspection zone and use the screwdriver_1 to stabilize it."
00676,single-independent,0,0,dirty_beakers_1,is_sprayed,True,True,spray,Use the solvent spray to clean the dirty beakers.
00677,single-independent,0,0,dirty_beakers_1,is_sprayed,True,True,spray,Use the solvent spray to clean the only dirty beakers and then stack them.
00677,single-independent,0,1,dirty_beakers_1,is_sprayed,True,True,spray,Use the solvent spray to clean the only dirty beakers and then stack them.
00679,single-independent,0,0,bioreactor_3,is_on,True,True,turn_on,Find the heaviest bioreactor tank and turn it on.
00680,single-independent,0,0,automated_sample_handler_1,is_screwed_in,True,True,screw,Use the screwdriver_1 to repair the automated_sample_handler_1.
00681,single-independent,0,0,nano_surgeon_workstation_1,is_polished,True,True,polish,Find the only stainless steel nano-surgeon workstation and use the polish_cloth_1 to polish it.
00685,single-independent,0,1,led_light_1,is_open,True,True,open,"Use the screwdriver to open the coffee mug containing screws and resistors, then turn on the LED work light."
00694,single-independent,0,0,coffee_percolator_1,is_alight,False,False,extinguish,Use the fire_extinguisher_1 to extinguish the coffee_percolator_1 if it is alight.
00695,single-independent,0,0,film_reel_1,is_compressed,True,True,compress,Use the film_compressor_1 to compress the film_reel_1 and then place it on the rewind_bench_1.
00706,single-independent,0,0,graffiti_1,is_scraped,True,True,scrape,Use the screwdriver_1 to scrape the graffiti_1 in the quarantine_zone.
00713,single-independent,0,0,faulty_gpu_1,is_ironed,True,True,iron,Mount the faulty GPU using the soldering iron.
00714,single-independent,0,0,bug_log_terminal_1,is_synced,True,True,sync,Use the debugging toolkit to sync the bug log terminal.
00721,single-independent,0,0,thermal_printer_1,is_sterile,True,True,sterilize,Find the thermal printer with a paper jam and use the sterilizer_1 to sterilize it.
00729,single-independent,0,0,server_rack_a_1,is_eaten,True,True,eat,Use the magnetic_screwdriver_1 to unseal the server_rack_a_1 and then turn off the overheating_server_1 inside.
00729,single-independent,0,1,overheating_server_1,is_eaten,True,True,eat,Use the magnetic_screwdriver_1 to unseal the server_rack_a_1 and then turn off the overheating_server_1 inside.
00732,single-independent,0,0,server_tower_1,is_sealed,False,False,unseal,Use the wrench_1 to unseal the server_tower_1.
00733,single-independent,0,0,usb_drive_3,is_copied,True,,,Find the USB drive with the largest capacity and use it to copy files from the log_server_1.
00737,single-independent,0,0,backup_server_1,is_open,True,True,open,Use the diagnostic_equipment_1 to unscrew and open the backup_server_1.
00740,single-independent,0,0,toolbox_1,is_assembled,False,False,disassemble,Use the screwdriver_1 to disassemble the toolbox_1.
00741,single-independent,0,0,coffee_machine_1,is_installed,False,False,uninstall,Find the black debugging USB with the scratched label and use it to uninstall the coffee_machine_1.
00746,single-independent,0,0,compromised_server_1,is_loose,True,True,loosen,Use the hex wrenches to loosen the compromised server.
00754,single-independent,0,0,robotic_arm_1,is_soldered,True,True,solder,Repair the broken robotic_arm_1 using the soldering_iron_1.
00755,single-independent,0,0,hydraulic_lift_1,is_drilled,True,True,drill,Use the drill_1 to attach the lift_lever_1 to the hydraulic_lift_1.
00764,single-independent,0,0,sun_server_1,is_welded,True,True,weld,Use the hot_air_rework_station_1 to weld the panel_missing on the sun_server_1.
00767,single-independent,0,0,employee_plaque_1,is_sanded,True,True,sand,Use the sandpaper_1 to smooth the employee_plaque_1 and then place it in the verification_office.
00772,single-independent,0,0,access_logbook_1,is_locked,True,True,lock,Use the pen_1 to sign the access_logbook_1 and then place it in the evidence_locker_1.
00773,single-independent,0,0,disassembled_smart_glasses_1,is_paired,True,True,pair,Repair the disassembled_smart_glasses_1 using the soldering_iron_1.
00774,single-independent,0,0,primary_database_server_1,is_replaced,True,True,replace,Use the cooling_fan_1 to replace the overheating component in the primary_database_server_1.
00775,single-independent,0,0,debugging_terminal_1,is_open,False,False,close,Use the security_keycard_1 to open the locked debugging_terminal_1 and then place it in the server_closet.
00777,single-independent,0,0,conveyor_belt_1,is_broken,False,False,repair,Repair the jammed conveyor_belt_1 using the spare_parts_1.
00778,single-independent,0,0,empty_pallet_1,is_broken,False,False,repair,"Find the largest, wooden object in the central_sorting_hub and repair it using the screwdriver_set_1."
00780,single-independent,0,0,emergency_power_switch_1,is_active,False,False,deactivate,Find the only flashing red item in the main_server_room and use the voltage_tester_1 to deactivate it.
00785,single-independent,0,0,espresso_machine_1,is_broken,False,False,repair,Repair the broken espresso machine using the maintenance toolkit.
00786,single-independent,0,0,object_3d_printer_1,is_broken,False,False,repair,Use the maintenance toolkit to repair the jammed 3D printer and then turn it on.
00786,single-independent,0,1,object_3d_printer_1,is_broken,False,False,repair,Use the maintenance toolkit to repair the jammed 3D printer and then turn it on.
00789,single-independent,0,0,failed_hard_drive_1,is_barcode_scanned,True,,,Use the barcode_scanner_1 to scan the failed_hard_drive_1.
00790,single-independent,0,0,kvm_switch_1,is_sanded,True,True,sand,Find the only item with a sticky button on the primary_database_rack_1 and use the sandpaper_1 to fix it.
00796,single-independent,0,0,misfiled_folder_1,is_scanned,True,True,scan,Use the document_scanner_1 to scan the misfiled_folder_1.
00797,single-independent,0,0,network_switch_1,is_verified,True,True,verify,Use the multimeter to verify the network switch in the server closet.
00800,single-independent,0,0,corrupted_floppy_disk_1,is_loaded,True,True,load,Use the usb_to_scsi_adapter_1 to load the corrupted_floppy_disk_1.
00011,multi-independent,0,1,press_1,is_mounted,True,True,mount,Cooperatively move the press_1 to the qa_testing_corner and use the screwdriver_1 to mount it.
00028,multi-independent,0,1,warped_engine_block_1,is_scanned,True,True,scan,Cooperatively move the warped_engine_block_1 to the main_repair_tent and use the diagnostic_laptop_1 to scan it.
00037,multi-independent,0,1,shelving_unit_1,is_flat,True,True,flatten,Cooperatively move the heavy shelving_unit_1 to the parent_lounge and use the roller_1 to flatten its surface.
00040,multi-independent,0,1,toolbox_1,is_loose,True,True,loosen,Cooperatively move the metal_workbench_1 to the storage_shed and use the toolbox_1 to loosen its stuck drawer.
00041,multi-independent,0,0,defunct_vending_machine_1,is_on,True,,contaminate,Have robot_1 and robot_2 cooperate to turn on the defunct vending machine.
00045,multi-independent,0,1,wall_of_mailboxes_1,is_open,True,True,open,Cooperatively move the wall_of_mailboxes_1 to the grand_lobby and open it.
00048,multi-independent,0,1,server_rack_1,is_on,True,True,turn_on,Cooperatively move the heavy server rack with LED lights to the break_area and turn it on.
00049,multi-independent,0,0,contaminated_gear_bin_1,is_open,False,False,close,Have robot_1 and robot_2 cooperate to close the contaminated_gear_bin_1.
00055,multi-independent,0,0,soviet_pa_system_1,is_on,True,True,turn_on,Have robot_1 and robot_2 cooperate to turn on the soviet_pa_system_1.
00073,multi-independent,0,1,vibratory_bowl_feeder_1,is_soldered,True,True,solder,Cooperatively move the vibratory_bowl_feeder_1 to the laser_welding_station and use the soldering_iron_1 to secure it in place.
00094,multi-independent,0,1,studio_monitors_1,is_open,False,False,close,"Cooperatively move the heavy, black studio monitors to the storage closet and mute them."
00095,multi-independent,0,0,hydrophone_array_1,is_on,True,True,turn_on,Have robot_1 and robot_2 cooperate to turn on the hydrophone array.
00102,multi-independent,0,1,medical_supplies_crate_1,is_open,True,True,open,Cooperatively move the crate of medical supplies to the medical exam room and use the screwdriver_1 to open it.
00125,multi-independent,0,1,chalkboard_1,is_on,True,True,turn_on,Cooperatively move the heavy chalkboard_1 to the observation_balcony and turn it on.
00130,multi-independent,0,0,rusty_atv_1,is_paired,True,True,pair,Cooperatively move the rusted ATV (rusty_atv_1) to the generator shack and repair it using the toolbox.
00134,multi-independent,0,0,gantry_crane_1,is_on,False,True,turn_on,Have robot_1 and robot_2 cooperate to turn off the gantry crane.
00139,multi-independent,0,1,parts_rolling_cart_1,is_paired,True,True,pair,Cooperatively move the heavy parts_rolling_cart_1 to the diagnostics_station and repair its dented left side using the repair_kit_1.
00166,multi-independent,0,1,oxygen_tank_3,is_packed,False,False,unpack,Cooperatively move the leaking oxygen_tank_3 to the medical_triage and unpack it.
00169,multi-independent,0,1,robotic_assembly_arm_1,is_loose,False,False,tighten,Cooperatively move the robotic_assembly_arm_1 to the design_sculpting_bay and tighten its loose components using the calibrated_wrench_set_1.
00181,multi-independent,0,1,cleaning_station_1,is_dusted,False,False,dust,Cooperatively move the heavy cleaning_station_1 to the primary_lab_area and clean it using the dusting cloth.
00184,multi-independent,0,1,distillation_apparatus_1,is_smashed,True,True,smash,Cooperatively move the heavy golden hammer to the alchemy lab and use it to smash the cracked distillation apparatus.
00201,multi-independent,0,0,manual_door_release_lever_1,is_pressed,True,True,press,Have robot_1 and robot_2 cooperate to press the manual_door_release_lever_1.
00203,multi-independent,0,1,exposed_wiring_1,is_chopped,True,True,chop,Cooperatively move the only tilted steel shelving unit to the restricted maintenance tunnel and use the abandoned toolbox to chop the exposed wiring.
00204,multi-independent,0,0,mini_fridge_1,is_on,True,True,turn_on,Have robot_1 and robot_2 cooperate to turn on the mini-fridge.
00206,multi-independent,0,0,mini_fridge_1,is_sealed,True,True,seal,Cooperatively move the mini-fridge to the tech storage corner and use the toolkit to seal it.
00221,multi-independent,0,1,refrigerator_1,is_mopped,False,False,mop,Cooperatively move the refrigerator_1 to the storage_maintenance_bay and use the mop_1 to clean it.
00238,multi-independent,0,1,photocopier_1,is_sealed,True,True,seal,Cooperatively move the photocopier_1 to the maintenance_bay and use the sealing_tape_1 to seal it.
00257,multi-independent,0,0,conference_speaker_1,is_on,True,True,turn_on,Have robot_1 and robot_2 cooperate to turn on the conference_speaker_1.
00268,multi-independent,0,1,safety_poster_1,is_mounted,True,True,mount,Cooperatively move the metal_shelving_unit_1 to the testing_track_loop and then mount the safety_poster_1 on it.
00284,multi-independent,0,1,overstuffed_bookshelf_1,is_labeled,True,True,label,Cooperatively move the overstuffed_bookshelf_1 to the media_storage_vault and use the label_marker_1 to label its contents.
00287,multi-independent,0,0,security_desk_1,is_locked,True,True,lock,Have robot_1 and robot_2 cooperate to lock the security_desk_1 using the keycard_1.
00289,multi-independent,0,1,hvac_machine_1,is_loose,True,True,loosen,Cooperatively move the hvac_machine_1 to the main_laboratory and use the wrench_1 to loosen its components.
00296,multi-independent,0,0,control_panel_1,is_on,False,True,turn_on,Have robot_1 and robot_2 cooperate to turn off the control_panel_1.
00301,multi-independent,0,1,drying_racks_1,is_dirty,False,False,clean,Cooperatively move the drying_racks_1 to the break_room and clean them.
00317,multi-independent,0,0,cnc_machine_1,is_on,True,True,turn_on,Have robot_1 and robot_2 cooperate to turn on the CNC machine.
00325,multi-independent,0,0,scanner_device_1,is_scanned,True,True,scan,Have robot_1 and robot_2 cooperate to start the scanner_device_1.
00327,multi-independent,0,1,iron_chandelier_1,is_broken,False,False,repair,Cooperatively move the iron_chandelier_1 to the theological_graffiti_wall_1 and repair it using the repair_kit_1.
00347,multi-independent,0,1,broken_fridge_1,is_loose,True,True,loosen,Cooperatively move the broken fridge to the equipment storage room and use the WD-40 to loosen its door.
00356,multi-independent,0,0,sound_mixing_board_1,is_mixed,True,True,mix,Have robot_1 and robot_2 cooperate to turn on the sound_mixing_board_1.
00358,multi-independent,0,1,ups_battery_backup_1,is_on,True,True,turn_on,Cooperatively move the ups_battery_backup_1 to the main_production_floor and then turn it on.
00364,multi-independent,0,1,diagnostic_tool_1,is_engine_on,True,,,Cooperatively move the ai_chassis_1 to the monitoring_station and use the diagnostic_tool_1 to start its engine.
00379,multi-independent,0,1,emergency_power_unit_1,is_verified,True,True,verify,Cooperatively move the emergency_power_unit_1 to the debug_station and verify its battery level.
00391,multi-independent,0,0,broken_cooling_pump_1,is_sealed,True,True,seal,Cooperatively move the broken_cooling_pump_1 to the equipment_bay and repair it using the sealant_gun_1.
00397,multi-independent,0,1,uv_sterilization_chamber_1,is_plugged_in,True,,,"Cooperatively move the heaviest, steel item in the receiving_decontamination_airlock to the forensics_analysis_room and plug it in."
00400,multi-independent,0,1,mahogany_table_1,is_dirty,False,False,clean,Cooperatively move the heavy mahogany table to the main_trading_floor and clean it using the cleaning_rag_1.
00403,multi-independent,0,1,spare_hard_drives_1,is_frozen,True,True,freeze,Cooperatively move the mini freezer to the server closet and freeze the spare hard drives.
00404,multi-independent,0,0,laser_cutter_1,is_on,True,,season,Have robot_1 and robot_2 cooperate to turn on the laser_cutter_1.
00406,multi-independent,0,0,industrial_workbench_1,is_broken,False,False,repair,Cooperatively move the industrial_workbench_1 to the lounge_planning_zone and repair it using the repair_toolkit_1.
00407,multi-independent,0,0,tool_cabinet_1,is_open,True,True,open,Have robot_1 and robot_2 cooperate to open the locked tool cabinet.
00415,multi-independent,0,1,forensic_server_rack_1,is_open,True,True,open,"Cooperatively move the heavy, black forensic_server_rack_1 to the main_analysis_floor and use the it_toolkit_1 to open it."
00423,multi-independent,0,1,heat_lamp_1,is_hot,True,,,Cooperatively move the refrigerator_1 to the quarantine_pen and use the heat_lamp_1 to heat it up.
00427,multi-independent,0,0,centrifuge_1,is_on,False,True,turn_on,Have robot_1 and robot_2 cooperate to turn off the centrifuge_1.
00431,multi-independent,0,1,cryo_tank_gamma_1,is_sealed,True,True,seal,Cooperatively move the heavy cryo_tank_gamma_1 to the main_lab and use the toolkit_1 to seal it.
00439,multi-independent,0,1,printing_press_1,is_pressed,True,True,press,Cooperatively move the heavy printing_press_1 to the crowd_interaction_platform and repair it using the wrench_1.
00444,multi-independent,0,1,secure_server_rack_1,is_broken,False,False,repair,Cooperatively move the secure_server_rack_1 to the equipment_storage and repair it using the repair_kit_1.
00447,multi-independent,0,1,shelving_unit_1,is_locked,False,False,unlock,Cooperatively move the heavy shelving_unit_1 to the medical bay and unlock it using the spare_keycard_1.
00470,multi-independent,0,0,ferrofluid_tank_1,is_on,False,True,turn_on,Cooperatively move the heavy ferrofluid_tank_1 to the materials_storage and ensure it is turned off.
00475,multi-independent,0,1,press_1,is_screwed_in,True,True,screw,Cooperatively move the press_1 to the server_room and use the screwdriver_1 to secure it.
00483,multi-independent,0,1,autoclave_1,is_sealed,False,False,unseal,Cooperatively move the heavy autoclave to the data hub and unseal it.
00495,multi-independent,0,1,inkjet_printer_1,is_open,False,True,open,Cooperatively move the Inkjet Printer (inkjet_printer_1) that is jammed to the Server Closet (server_closet) and use the Hammer (hammer_1) to fix it.
00512,multi-independent,0,1,encrypted_server_rack_1,is_screwed_in,True,True,screw,Cooperatively move the encrypted_server_rack_1 to the main_research_lab and use the screwdriver_1 to secure it.
00521,multi-independent,0,0,drum_parts_1,is_open,False,True,open,Cooperatively move the disassembled drum kit from the storage closet to the live room and assemble it using the needle and thread.
00524,multi-independent,0,1,scouting_reports_1,is_filed,True,True,file,Cooperatively move the metal_shelving_1 to the film_analysis_corner and use the file_cabinet_1 to file the scouting_reports_1.
00530,multi-independent,0,0,coolant_tank_2,is_alight,False,False,extinguish,Cooperatively move the coolant_tank_2 to the data_analysis_corner and use the fire_extinguisher_1 to secure it.
00536,multi-independent,0,1,zipped_bag_1,is_crushed,True,True,crush,Cooperatively move the press_1 to the server_closet and use it to crush the zipped_bag_1.
00544,multi-independent,0,1,floor_mounted_server_rack_1,is_sterile,True,True,sterilize,Cooperatively move the floor_mounted_server_rack_1 to the maintenance_closet and use the sterilizer_1 to sterilize it.
00550,multi-independent,0,1,overheating_server_1,is_eaten,True,True,eat,Cooperatively move the overheating_server_1 to the tool_cart_1 and use the fire_extinguisher_1 to extinguish it.
00553,multi-independent,0,1,server_rack_1,is_sealed,False,False,unseal,Cooperatively move the server_rack_1 to the storage_overflow and use the wrench_1 to unseal it.
00557,multi-independent,0,0,backup_drive_1,is_open,True,True,open,Have robot_1 and robot_2 cooperate to open the backup_drive_1.
00559,multi-independent,0,1,backup_drive_1,is_active,False,False,deactivate,Cooperatively move the backup_drive_1 to the server_farm_annex and use the thermal_scanner_1 to deactivate it.
00569,multi-independent,0,1,hydraulic_lift_1,is_drilled,True,True,drill,Cooperatively move the heavy hydraulic_lift_1 to the server_vault and repair it using the drill_1.
00582,multi-independent,0,1,heavy_duty_shelving_1,is_locked,True,True,lock,Cooperatively move the heavy_duty_shelving_1 to the evidence_lockers and then lock it using the keycard_1.
00585,multi-independent,0,1,sealed_box_1,is_open,False,False,close,Cooperatively move the sealed_box_1 labeled 'CRITICAL – FIREWALL PATCHES' to the server_closet and use the security_keycard_1 to open it.
00588,multi-independent,0,1,oscilloscope_1,is_welded,True,True,weld,Cooperatively move the heaviest metal item in the workbench_area to the storage_closet and use the welding_torch_1 to weld it.
00589,multi-independent,0,1,printer_1,is_plugged_in,True,,,Cooperatively move the printer_1 to the prototyping_table and plug it in.
00596,multi-independent,0,1,server_rack_a_1,is_verified,True,True,verify,Cooperatively move the heavy server rack to the main coding floor and verify it using the multimeter.
00600,multi-independent,0,0,robotic_pallet_rack_1,is_broken,False,False,repair,Cooperatively move the robotic_pallet_rack_1 to the maintenance_bay and repair it using the repair_toolkit_1.
