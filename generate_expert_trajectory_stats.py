#!/usr/bin/env python3
"""
Expert Trajectory Statistics Generator

This script analyzes task files in data/eval/ directories and generates
statistics about expert trajectories including task_id, task_category,
agent_type (Single/Multi), and expert_trajectory_steps.

Rules for expert trajectory steps:
- Move operations (with location_id): 6 steps
- Non-move operations without tools: 4 steps  
- Non-move operations with tools: 6 steps
"""

import json
import csv
import os
from pathlib import Path
from typing import Dict, List, Tuple


def load_attribute_actions_mapping(csv_path: str) -> Dict[str, bool]:
    """Load the attribute to requires_tool mapping from CSV file."""
    mapping = {}
    
    with open(csv_path, 'r', encoding='utf-8') as f:
        reader = csv.DictReader(f)
        for row in reader:
            attribute = row['attribute']
            requires_tool = row['requires_tool'].lower() == 'true'
            mapping[attribute] = requires_tool
    
    return mapping


def extract_task_id(filename: str) -> str:
    """Extract task ID from filename (first 5 digits)."""
    return filename[:5]


def determine_agent_type(agents_config: List[Dict]) -> str:
    """Determine if task is Single or Multi agent."""
    return "Single" if len(agents_config) == 1 else "Multi"


def calculate_expert_steps(validation_checks: List[Dict], attr_mapping: Dict[str, bool]) -> int:
    """Calculate expert trajectory steps based on validation checks."""
    for check in validation_checks:
        # Check if it's a move operation (has location_id)
        if 'location_id' in check:
            return 6
        
        # Find the attribute being validated (excluding 'id')
        for key, value in check.items():
            if key != 'id' and key in attr_mapping:
                # Non-move operation: check if requires tool
                requires_tool = attr_mapping[key]
                return 6 if requires_tool else 4
    
    # Default case (shouldn't happen with valid data)
    return 4


def process_task_file(file_path: str, attr_mapping: Dict[str, bool]) -> Tuple[str, str, str, int]:
    """Process a single task file and return statistics."""
    with open(file_path, 'r', encoding='utf-8') as f:
        data = json.load(f)
    
    # Extract task ID from filename
    filename = os.path.basename(file_path)
    task_id = extract_task_id(filename)
    
    # Get task category
    task_category = data['tasks'][0]['task_category']
    
    # Determine agent type
    agent_type = determine_agent_type(data['agents_config'])
    
    # Calculate expert trajectory steps
    validation_checks = data['tasks'][0]['validation_checks']
    expert_steps = calculate_expert_steps(validation_checks, attr_mapping)
    
    return task_id, task_category, agent_type, expert_steps


def process_directory(dir_path: str, attr_mapping: Dict[str, bool]) -> List[Tuple[str, str, str, int]]:
    """Process all task files in a directory."""
    results = []
    task_dir = Path(dir_path)
    
    if not task_dir.exists():
        print(f"Warning: Directory {dir_path} does not exist")
        return results
    
    # Get all JSON files and sort them
    json_files = sorted([f for f in task_dir.glob("*.json") if f.name.endswith("_task.json")])
    
    for file_path in json_files:
        try:
            result = process_task_file(str(file_path), attr_mapping)
            results.append(result)
        except Exception as e:
            print(f"Error processing {file_path}: {e}")
    
    return results


def main():
    """Main function to generate expert trajectory statistics."""
    # Load attribute actions mapping
    attr_mapping = load_attribute_actions_mapping("data/attribute_actions.csv")
    
    # Process both directories
    single_results = process_directory("data/eval/single-independent/task", attr_mapping)
    multi_results = process_directory("data/eval/multi-independent/task", attr_mapping)
    
    # Combine results
    all_results = single_results + multi_results
    
    # Sort by task_id
    all_results.sort(key=lambda x: x[0])
    
    # Write to CSV
    output_file = "expert_trajectory_statistics.csv"
    with open(output_file, 'w', newline='', encoding='utf-8') as f:
        writer = csv.writer(f)
        
        # Write header
        writer.writerow(['task_id', 'task_category', 'agent_type', 'expert_trajectory_steps'])
        
        # Write data
        for result in all_results:
            writer.writerow(result)
    
    print(f"Generated {output_file} with {len(all_results)} entries")
    print(f"Single-agent tasks: {len(single_results)}")
    print(f"Multi-agent tasks: {len(multi_results)}")


if __name__ == "__main__":
    main()
