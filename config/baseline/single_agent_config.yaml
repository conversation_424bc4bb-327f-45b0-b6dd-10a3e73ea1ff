# 单智能体配置文件
extends: "base_config"

# 数据集选择配置
dataset:
  # 默认使用的数据集 (eval_single, eval_multi, sft_single, sft_multi, source)
  default: "eval_single"

# 日志配置
logging:
  level: "DEBUG"
  show_llm_details: true

# 智能体配置
agent_config:
  agent_class: "modes.single_agent.llm_agent.LLMAgent"
  max_history: 20

  # 环境描述配置
  environment_description:
    detail_level: 'full'
    show_object_properties: true
    only_show_discovered: true
    include_other_agents: true
    update_frequency: 0

# 覆盖基础配置
execution:
  max_total_steps: 400
  max_steps_per_task: 35

evaluation:
  task_type: 'independent'
  default_scenario: '00002'



parallel_evaluation:
  enabled: true
  scenario_parallelism:
    max_parallel_scenarios: 5
  scenario_selection:
    mode: 'all'
    range:
      start: '00000'
      end: '01000'
    list: ['00001', '00002', '00004', '00006', '00007', '00008', '00009', '00010']



