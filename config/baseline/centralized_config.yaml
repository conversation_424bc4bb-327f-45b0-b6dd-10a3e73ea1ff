# 中心化多智能体配置文件
extends: "base_config"

# 数据集选择配置
dataset:
  # 默认使用的数据集 (eval_single, eval_multi, sft_single, sft_multi, source)
  default: "eval_multi"

# 日志配置
logging:
  level: "DEBUG"
  show_llm_details: true

# 智能体配置
agent_config:
  agent_class: "modes.centralized.centralized_agent.CentralizedAgent"
  max_history: 35

  environment_description:
    detail_level: 'full'
    show_object_properties: true
    only_show_discovered: true
    include_other_agents: true
    update_frequency: 0

# 历史记录格式配置
history:
  format:
    include_thought: false
    show_execution_status: false



parallel_evaluation:
  scenario_parallelism:
    max_parallel_scenarios: 1
  scenario_selection:
    mode: 'all'
    range:
      start: '00250'
      end: '00256'
    list: ['00402']