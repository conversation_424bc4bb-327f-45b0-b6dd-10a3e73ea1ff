# LLM配置文件
extends: "base_config"

# LLM推理模式设置
mode: "api"

# API调用配置
api:
  # 当前使用的提供商
  provider: "deepseek"

  # 供应商配置（统一结构）
  providers:
    
    volcengine:
      model: "gemini-2.5-flash"
      temperature: 0.3
      max_tokens: 4096
      api_key: "sk-D8vrKhWyUqLl0VAkKSfJ90GRz6jC83j7Xwnr2j6XFPnPnSf0"
      endpoint: "https://api2.aigcbest.top/v1"

    bailian:
      model: "Qwen2.5-72B-Instruct-GPTQ-Int4"
      temperature: 0.3
      max_tokens: 512
      api_key: "dummy_key"
      endpoint: "http://10.130.129.18:8721/v1"
      extends:
        guided_regex: "Thought: .+\\n+\\s*Agent_1_Action: .+(?:\\n+\\s*Agent_2_Action: .+)?"
        stop_tokens: []

    deepseek:
      model: "deepseek-chat"
      temperature: 0.1
      max_tokens: 512
      api_key: "sk-ec0c6aa1a50a49b99ae1ba48eb46cc56"
      endpoint: "https://api.deepseek.com"

    deepseekv3:
      model: "deepseek-chat"
      temperature: 0.3
      max_tokens: 2048
      api_key: "sk-234769c45cab419a95fcf6c1b8e7663b"
      endpoint: "https://api.deepseek.com"

    deepseekr1:
      model: "deepseek-reasoner"
      temperature: 0.3
      max_tokens: 65536
      timeout: 3600
      api_key: "sk-234769c45cab419a95fcf6c1b8e7663b"
      endpoint: "https://api.deepseek.com"

    qwen06b:
      model: "Qwen3-0.6B"
      temperature: 0.3
      max_tokens: 512
      api_key: "dummy_key"
      endpoint: "http://10.130.138.49:8006/v1"
      extends:
        guided_regex: "Thought: .+\\n+\\s*Agent_1_Action: .+(?:\\n+\\s*Agent_2_Action: .+)?"
        stop_tokens: []

    qwen3b:
      model: "Qwen2.5-3B-Instruct"
      temperature: 0.3
      max_tokens: 512
      api_key: "dummy_key"
      endpoint: "http://10.15.82.49:8003/v1"
      extends:
        guided_regex: "Thought: .+\\n+\\s*Agent_1_Action: .+(?:\\n+\\s*Agent_2_Action: .+)?"
        stop_tokens: []

    qwen7b:
      model: "Qwen2.5-7B-Instruct"
      temperature: 0.3
      max_tokens: 512
      api_key: "dummy_key"
      endpoint: "http://10.15.82.46:8007/v1"
      extends:
        guided_regex: "Thought: .+\\n+\\s*Agent_1_Action: .+(?:\\n+\\s*Agent_2_Action: .+)?"
        stop_tokens: []

    qwen72b:
      model: "Qwen2.5-72B-Instruct-GPTQ-Int4"
      temperature: 0.3
      max_tokens: 512
      api_key: "dummy_key"
      endpoint: "http://10.130.129.18:8720/v1"
      extends:
        guided_regex: "Thought: .+\\n+\\s*Agent_1_Action: .+(?:\\n+\\s*Agent_2_Action: .+)?"
        stop_tokens: []

    llama8b:
      model: "Meta-Llama-3.1-8B-Instruct"
      temperature: 0.3
      max_tokens: 512
      api_key: "dummy_key"
      endpoint: "http://10.15.82.43:8033/v1"
      extends:
        guided_regex: "Thought: .+\\n+\\s*Agent_1_Action: .+(?:\\n+\\s*Agent_2_Action: .+)?"
        stop_tokens: []

# VLLM本地推理配置
vllm:
  model_path: "/path/to/model"
  temperature: 0.1
  max_tokens: 4096
  tensor_parallel_size: 1
  gpu_memory_utilization: 0.9

# LLM参数配置
parameters:
  send_history: false