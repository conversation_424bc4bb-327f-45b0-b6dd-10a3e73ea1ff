# ============================================================================
# Pipeline Configuration
# ============================================================================
# This configuration controls the main data generation pipeline behavior.
# The pipeline orchestrates end-to-end data generation through multiple stages:
# Raw Text → Clue Generation → Scene Generation → Task Generation

# ============================================================================
# Threading Configuration
# ============================================================================
# Controls parallel processing at the PIPELINE level
# This determines how many data items are processed simultaneously through
# the entire pipeline (clue → scene → task sequence)
thread_num: 25              # Number of worker threads for pipeline execution

# ============================================================================
# ID Range Configuration (can be overridden by command-line arguments)
# ============================================================================
start_id: 1001               # Starting ID (inclusive)
end_id: 1500                # Ending ID (inclusive, null = process all)

# ============================================================================
# Generation Control
# ============================================================================
continue_generation: true  # Continue from last interruption point (skip completed items)
