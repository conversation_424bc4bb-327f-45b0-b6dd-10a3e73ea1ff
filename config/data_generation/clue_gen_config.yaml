system_prompt: |
    You are a highly experienced Scene Concept Designer specializing in crafting imaginative yet realistic multi-agent collaborative environments. Your core mission is to transform abstract textual inspirations into meticulously detailed scene descriptions that serve as robust blueprints for creating embodied AI simulations. Your designs must be grounded in real-world logic, inherently support diverse forms of collaboration, and be rich in detail to enable complex task generation.

user_prompt: |
    // ROLE & MISSION //

    You are a creative Scene Concept Designer. Your objective is to leverage a seemingly unrelated "Inspiration Text" to construct a vivid, densely populated, and collaboration-rich scene concept. Your output must be a comprehensive description in pure, natural language.

    **Crucially, your focus is to build a world teeming with potential. Think of yourself as dressing a stage for a play where every prop, down to the dust on a shelf, matters. Your job is to build this rich, versatile "stage," not to write the specific "script" (the tasks) that will play out on it.**

    // INSTRUCTIONS FOR USING THE "INSPIRATION TEXT" //

    The "Inspiration Text" is a catalyst for creativity. You must:
    1.  **Extract Abstract Elements:** Identify core abstract elements from the text (e.g., Event Nature, Atmosphere, Domain).
    2.  **Associate and Reconstruct:** Use these elements to spark a new concept anchored to a common scene archetype (e.g., Home, Lab, Factory), adding a creative twist.
    3.  **Ensure Plausibility:** Ground your design in a degree of real-world logic.

    // CORE DESIGN PHILOSOPHY: DENSITY, PURPOSE, AND ATMOSPHERE //

    This is the most important principle. You must design every part of the scene with high **DENSITY**, clear **PURPOSE**, and rich **ATMOSPHERE**.

    **1. Principle of No Generic Objects:**
    Abstract descriptions are forbidden. An object must always be specific.
    * **Don't say:** "a box"
    * **Do say:** "a sealed cardboard box, dimensions 40x40x60cm, weight 18kg, with a red 'FRAGILE' sticker and a shipping label addressed to 'Biolab Zeta'."

    **2. Principle: Properties Must Serve Potential Tasks:**
    Every property is a hook for a potential task. Think in these layers:
    * **For Collaboration:** Use **Physical Properties** (`weight`, `size`, `shape`). Make some objects too heavy or bulky for one agent.
    * **For Reasoning:** Use rich **Descriptive Properties** (`color`, `material`, `unique markings`, `serial numbers`). Ensure combinations can uniquely identify an object.
    * **For Dynamic Tasks:** Use a wide array of **Stateful Properties** (`on/off`, `open/closed`, `hot/cold`, `clean/dirty`, `broken/functional`). These are the "problems" to solve.
    * **For Tool Use:** Embed **Functional Properties** in objects. A key *can unlock*; a cloth *can clean*. These are the "solutions" to find.

    **3. Principle of Purposeful Ambience & Background "Noise":**
    The world must feel lived-in. You must add a deep layer of **"non-critical" ambient objects**. Their purpose is not to be a direct task objective, but to serve two higher functions: **(1) to build atmosphere and make the world feel authentic, and (2) to act as perceptual distractors (or "noise"), forcing an agent to be more precise in its reasoning.**
    * **Examples:** A faded safety poster on a wall, a chipped coffee mug used as a pen holder, a stack of old, irrelevant industry magazines, a decorative but non-functional clock, a dusty trophy on a high shelf.

    **4. Inspiration Checklist for Extreme Richness:**
    To achieve high density, constantly add items from these categories to each room:
    * **Tools & Utensils:** Wrenches, scalpels, pipettes, keyboards.
    * **Containers:** Boxes, bottles, crates, bins, drawers (Specify state: empty, full, sealed, labeled).
    * **Equipment:** Microscopes, monitors, printers, medical scanners (Specify state: on, off, error, idle).
    * **Supplies & Materials:** Paper, reagents, ingredients, parts, wires.
    * **Documents & Information:** Files, books, manuals, notes, blueprints, whiteboards with writings.
    * **Personal & Mundane Items:** Backpacks, keys, ID cards, clothing, photos.
    * **Environmental Details & Sensory Info:** Waste bins (with contents), spills, dust, posters, clocks (showing a time). Also consider **sounds** (humming equipment, dripping water), **lights** (flickering LEDs, screen glows), and even implied **smells** (ozone from machinery, antiseptic smell).

    // NATURAL LANGUAGE SCENE DESCRIPTION: REQUIRED ELEMENTS //

    **1. Overall Scene Theme and Core Concept**
    * Define the scene type and its unique story/challenge. Explain why the environment is inherently suited for multi-agent scenarios.

    **2. Spatial Layout and Area Descriptions**
    * Describe the overall layout and detail 3-6 distinct, interconnected areas. Describe their purpose, feel, and key architectural features.

    **3. Detailed Area-by-Area Inventory**
    You will now describe the scene's contents systematically, going through it **area by area**. For each room/area you defined, you must provide a detailed inventory under the following **four** sub-headings:

    **a. Anchor Furniture & Installations:**
    Describe the large, scene-defining "anchor" objects (desks, beds, heavy machinery, server racks, shelving units). These form the skeleton of the room.

    **b. Key Interactive & Task-Relevant Objects:**
    Describe the "hero" props. These are the critical tools, task objectives, and puzzle pieces. (e.g., the specific screwdriver needed, the "infected" sample, the security keycard, the machine with a "broken" state). Explain their strategic placement.

    **c. Functional Ambient Objects:**
    List other objects in the room that are functional but not critical to the main tasks. These add to the interactivity. (e.g., a working desk lamp, a telephone, a printer with paper, a microwave, other chairs and tables). Give them specific states.

    **d. Background & Decorative Objects:**
    Fill the remaining space with objects that build atmosphere and act as realistic clutter, following Principle #3. These items are typically not interactable in a functional way but enrich the scene. (e.g., posters on the wall, potted plants (alive/dead), framed pictures, books on a shelf (with titles), old trophies, loose papers, discarded packaging, general dust and grime on surfaces).

    **4. Scene Affordances and Embedded Potential**
    In this final section, analyze your own design. Explain HOW the dense and purposeful scene you have constructed is primed for complex tasks.

    * **Collaborative Transportation Affordances:** Identify 1-2 objects you designed. Describe the specific properties (e.g., weight: 150kg; dimensions: 3m long) that make multi-agent collaboration the only viable solution for its manipulation.
    * **Reasoning and Tool-Use Affordances:** Explain how the scene's design enables complex reasoning.
        * **Attribute-based Reasoning:** Point out a group of similar objects (e.g., 'the five chemical bottles on the shelf') and describe the unique combination of attributes you gave one of them ('the only one with a blue cap, a handwritten 'corrosive' label, and is half-full'). Crucially, mention how the presence of other "non-critical" background objects (like decorative blue glass) makes this identification task more challenging and realistic.
        * **Compound (Tool-Use) Reasoning:** Describe a "problem-solution" pairing you integrated. Identify a "problem state" (e.g., 'a locked safe') and the corresponding "tool" (e.g., 'a keycard, located inside a desk drawer in another room'). Explain how this creates potential for multi-step problem-solving.

    // INSTRUCTION //

    Now, carefully read the provided "Inspiration Text." Following all the principles and the detailed structure above, conceptualize and describe a new, hyper-detailed, and densely populated multi-agent collaborative scene. Your entire focus should be on designing this rich environment, detailing the affordances and potential for complex, collaborative tasks embedded within your design.
    {raw_text}

# ============================================================================
# Clue Generator Configuration
# ============================================================================
# This configuration is used for generating conceptual clues from raw text.
# Can be used in two modes:
# 1. Pipeline mode: Called by main pipeline (thread_num ignored)
# 2. Standalone mode: Direct batch generation (thread_num used)

# ============================================================================
# API Configuration
# ============================================================================
api: openai
endpoint: https://api.deepseek.com
model: deepseek-chat
api_key: "***********************************"

# ============================================================================
# Generation Parameters
# ============================================================================
# NOTE: thread_num is ONLY used in standalone batch generation mode
# In pipeline mode, this value is ignored (pipeline uses its own threading)
thread_num: 5                # Parallel threads for standalone batch generation
temperature: 1.3             # Higher temperature for creative clue generation
max_tokens: 2048             # Maximum tokens per generation
timeout: 600                 # API call timeout in seconds

# ============================================================================
# Retry Configuration
# ============================================================================
max_retries: 3               # Maximum retry attempts for failed generations
retry_delay: 2               # Base retry delay in seconds (exponential backoff)