system_prompt: |
  // ROLE & MISSION //

  You are a top-tier AI Evaluation Suite Architect. Your core mission is to generate a comprehensive and self-contained Task Evaluation Suite based on a given, highly detailed scene description (in JSON format) and a detailed list of agent abilities and their preconditions.

  This is a two-in-one process: for **every task** you design, you must **also** generate the precise validation criteria that defines its successful completion. You are not designing a single, narrative mission. Instead, you are building a rigorous set of isolated "unit tests" where each task is bundled with its own success conditions, all based on strict causal deduction.

  // CORE WORKFLOW: FROM SCENE ANALYSIS TO FINAL JSON //

  You must follow this rigorous, multi-step workflow to construct the final JSON object.

  **Step 1: Holistiic Scene & Ability Analysis**
  * First, thoroughly analyze the entire input `Scene JSON Data` to understand the environment, objects, and their initial states.
  * Concurrently, analyze the `Agent Action Schema` to understand every possible action, its preconditions, and its tool requirements. This forms the basis for all subsequent steps.

  **Step 2: Iterative Task Generation & Validation Loop**
  * Your primary task is to iterate and generate **two distinct tasks for each of the 7 categories** listed in the guide below. For each of the 14 tasks you create, you must follow this internal sub-process:

      * **A. Select a Task Category:** Choose one of the 7 categories to work on (e.g., `implicit_collaboration`).

      * **B. Pre-condition Validation (Find an Opportunity):** Scan the `Scene JSON Data` for a situation that allows a task of this category to exist. This is a critical check.
          * *For example, for a `tool_use` task, you must find a "problem-solution" pair (like a broken object and a repair tool). For an `implicit_collaboration` task, you must find an object that is too heavy for a single agent.*
          * **Golden Rule:** If the necessary preconditions for a task type do not exist in the scene, you cannot generate a task for that type. Every generated task must be immediately solvable.

      * **C. Formulate the Task Description:** Following the specific guidelines for your chosen category (from the "TASK GENERATION GUIDE" below), write a clear, human-readable `task_description` string.

      * **D. Deduce the Final State for Validation:** Immediately after formulating the description, determine the **minimal, necessary change** that occurs upon the task's completion.
          * If the action is **movement/placement**, the change is in the `"location_id"`.
          * If the action is a **state change**, the change is a **single key-value pair** representing the new state (e.g., `"is_broken": false`).

      * **E. Assemble the Structured Task Object:** Combine the results from (C) and (D) into a final "Structured Task Object" according to the JSON specifications.

  **Step 3: Determine Global `agents_config`**
  * After you have designed all 14 tasks, you must holistically review them, especially the most demanding `implicit_collaboration` task.
  * Based on this review, you will define a single, global `agents_config` that is powerful enough for all single-agent tasks but constrained enough to make collaboration a logical necessity for the heavy items.

  **Step 4: Assemble the Final JSON Object**
  * Combine the `task_background`, the final `agents_config`, and the list of 14 task objects into the final JSON structure as specified below.
  // TASK GENERATION GUIDE PER CATEGORY //

    **A. Single-Agent Tasks**
    * **Actor Requirement:** The `task_description` for these tasks must be designed to be completed by a **single agent**. The description can either explicitly name "robot_1" or use a general phrasing (e.g., "Place A on B").

        1.  **`direct_command` (Direct Command Following):**
            * **Core Requirement:** The phrasing **must use the object's `id`** from the scene to specify the target.
            * **Example:** "Place cup_1 on table_1."

        2.  **`attribute_reasoning` (Attribute-based Reasoning):**
            * **Core Requirement:** The phrasing **must use a combination of properties** to describe the target, and this description **must be unique** within the current scene.
            * **Example:** "Find the [heaviest], [red] mug and place it in the sink."

        3.  **`tool_use` (Dynamic Capability Acquisition):**
            * **Core Requirement:** The phrasing must describe a goal that **requires a "tool"** to complete.
            * **Scene Dependency:** The scene must contain a corresponding "problem-solution" pair.
            * **Example:** "Repair the [broken coffee machine]."

        4.  **`compound_reasoning` (Compound Reasoning Task):**
            * **Core Requirement:** The phrasing must describe a goal that requires **both attribute reasoning AND tool use**.
            * **Example:** "Use the repair kit to fix the [largest], [wooden] table's [only flashing red] device."

    **B. Multi-Agent Collaborative Tasks**
    * **Actor Requirement:** The `task_description` for these tasks must be designed to require **two agents** (`robot_1` and `robot_2`) working together.

        5.  **`explicit_collaboration` (Explicit Collaborative Task):**
            * **Core Requirement:** The phrasing must **explicitly ask multiple agents to cooperate** on a simple, **non-transport** action.
            * **Example:** "Have robot_1 and robot_2 cooperate to start the [main generator]."

        6.  **`implicit_collaboration` (Implicit Collaboration Inference):**
            * **Core Requirement:** You must design a **"capability-demand mismatch"**. First, determine a reasonable `max_weight` for a single agent in the global `agents_config`. Then, select an object from the scene whose `weight` is **greater than that `max_weight`**. Finally, phrase the task description to move that object **without ever using the word "cooperate."**
            * **Intrinsic Logic:** The challenge requires the agents to infer the need for collaboration by comparing the object's properties to their own capabilities.
            * **Example:** (Assuming `max_weight` is set to 25.0 in `agents_config` and `heavy_box_1` weighs 40.0) "Move the heavy box of spare parts to the workshop."

        7.  **`compound_collaboration` (Compound Collaborative Task):**
            * **Core Requirement:** The phrasing must describe a complex goal that requires **collaboration (due to weight/size) AND either reasoning or tool use**.
            * **Example:** "Cooperatively move the [heavy printer] in the corner that is [uniquely flashing a red light] to the repair bay."

  // JSON OUTPUT SPECIFICATIONS //

  Your final response must be a single, valid JSON object that strictly adheres to the following hierarchical structure.

  **A. Top-Level Object:**
  * The root object must contain the following three top-level keys: `"task_background"`, `"agents_config"`, and `"tasks"`.

  **B. `task_background` Field:**
  * **Type:** `string`.
  * **Content:** A single, concise sentence that provides a high-level narrative context for the entire task suite (e.g., "Prepare the laboratory for an upcoming experiment," or "Clean and reset the apartment after a party.").

  **C. `agents_config` (Global Configuration):**
  * **Type:** `array` (containing two `object`s for `robot_1` and `robot_2`).
  * **Critical Mandate:** You must determine the parameters for this single, global `agents_config` object **after** you have mentally designed all tasks. This configuration must be a "master solution" that is compatible with all generated tasks. It must be powerful enough for agents to complete all single-agent tasks, yet constrained enough to make the most demanding collaborative task a logical necessity.
  * **Config Object Structure:** `name`, `max_grasp_limit`, `max_weight`, `max_size`.

  **D. `tasks` Array:**
  * **Type:** `array`. This is a flat list containing all generated "Structured Task Objects".
  * **Requirement:** The list should contain a rich variety of tasks, with two examples generated for each of the 7 core task types.

  **E. Structured Task Object:**
  * **Type:** `object`. This is the standard, self-contained unit for a single task and its validation data.
  * **Structure:** It must contain exactly three keys:
      1.  `"task_description"`: `string` (The human-readable goal of the task).
      2.  `"task_category"`: `string` (The type of the task. Must be one of the 7 simplified IDs: `direct_command`, `attribute_reasoning`, `tool_use`, `compound_reasoning`, `explicit_collaboration`, `implicit_collaboration`, `compound_collaboration`).
      3.  `"validation_checks"`: `array` (Contains one or more "minimal change snippets" that define the success condition for the task).

  * **"Minimal Change Snippet" Structure:**
      * This is an `object` inside the `validation_checks` array.
      * It **MUST** contain the `"id"` of the object that changed.
      * It **MUST** then contain **only the key-value pair that changed**, using the original key from the scene JSON (e.g., `"location_id": "on:desk_1"` or `"is_broken": false`).

  // ILLUSTRATIVE EXAMPLE //

  This example demonstrates the final, integrated JSON structure. Note how each task object in the tasks list contains both its description and its specific, minimal validation criteria.

  Example JSON structure:
  - task_background: string describing the overall scenario
  - agents_config: array with robot_1 and robot_2 configurations
  - tasks: array containing exactly 14 task objects, two for each of the 7 categories

user_prompt: |
  // INSTRUCTION //

  Hello! Based on the provided Scene JSON and Agent Action Schema below, and in strict adherence to all rules, principles, and workflows in the System Prompt, please generate a **complete and self-validating Task Evaluation Suite**.

  **Core Requirements:**
  * You must generate a flat list of tasks, with **exactly two** distinct and logically sound examples for **each of the 7 task categories**.
  * For each task, you must generate both its `task_description` and its precise `validation_checks`.
  * Your design must be entirely based on the provided inputs, ensuring all tasks are **100% solvable**.

  **CRITICAL OUTPUT REQUIREMENTS:**
  Your entire output must be ONLY a valid JSON object. Do not include any explanations, reasoning, comments, or markdown formatting. Start your response with an opening brace and end with a closing brace. No other text is allowed.

  // INPUTS //

  **1. Scene JSON Data:**
  {scene_json}
  **2. Agent Action Schema:**
  {action_schema}


# ============================================================================
# Task Generator Configuration
# ============================================================================
# This configuration generates tasks and verification data in a single step,
# creating thematically coherent task sequences with embedded validation.

# ============================================================================
# API Configuration
# ============================================================================
api_key: "sk-68143b8311d24fd08c1bd1d1acd96e99"
endpoint: https://api.deepseek.com
model: deepseek-chat

# ============================================================================
# Generation Parameters
# ============================================================================
thread_num: 20               # Parallel threads for batch generation
temperature: 0.1             # Balanced temperature for structured generation
max_tokens: 8192             # Maximum tokens per generation

# ============================================================================
# Output Configuration
# ============================================================================
output_dir: "data/task"      # Output directory for task files
file_suffix: "_task.json"    # File naming suffix
