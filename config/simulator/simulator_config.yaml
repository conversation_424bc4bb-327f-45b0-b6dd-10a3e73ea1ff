# 模拟器全局配置
explore_mode: thorough   # normal/thorough
global_observation: false  # 是否启用全局观察模式（所有物品初始即被发现）

# 任务验证配置
task_verification:
  enabled: true          # 是否启用任务验证功能
  mode: "step_by_step"   # 验证模式: step_by_step/global/disabled
                         # step_by_step: 每步都进行验证并返回子任务完成状态
                         # global: 只在输入done命令时进行全局验证
                         # disabled: 不进行任何任务验证
  return_subtask_status: true      # 是否在每步返回子任务完成状态（仅step_by_step模式有效）

# 可视化配置
visualization:
  enabled: false          # 是否启用可视化系统
  web_server:
    host: "localhost"     # Web服务器主机地址
    port: 8080           # Web服务器端口
    auto_open_browser: true  # 是否自动打开浏览器
  request_interval: 2000  # 前端HTTP请求间隔 (毫秒) - 控制界面更新频率
 
